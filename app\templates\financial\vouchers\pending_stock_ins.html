{% extends "financial/base.html" %}

{% block title %}待生成凭证的入库单{% endblock %}

{% block extra_css %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
/* 待生成凭证入库单页面优化样式 */
.pending-stock-ins-table {
    font-size: 0.9rem;
}

.pending-stock-ins-table td {
    vertical-align: middle;
    padding: 0.75rem 0.5rem;
}

.pending-stock-ins-table .stock-in-number {
    font-weight: 600;
    color: #007bff;
    line-height: 1.3;
}

.pending-stock-ins-table .purchase-order-link {
    display: inline-block;
    line-height: 1.3;
    word-break: break-all;
}

.pending-stock-ins-table .purchase-order-link:hover {
    text-decoration: underline !important;
}

.pending-stock-ins-table .amount-cell {
    font-weight: 600;
    font-size: 0.95rem;
}

.pending-stock-ins-table .btn-group-sm .btn {
    padding: 0.25rem 0.4rem;
    font-size: 0.8rem;
}

/* 表格行悬停效果 */
.pending-stock-ins-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .pending-stock-ins-table {
        font-size: 0.8rem;
    }

    .pending-stock-ins-table td {
        padding: 0.5rem 0.3rem;
    }
}
</style>
{% endblock %}

{% block financial_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">待生成凭证的入库单</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回凭证管理
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if stock_ins %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> 
                        <strong>说明：</strong>以下入库单已完成财务确认，可以生成财务凭证。点击"生成凭证"为单个入库单生成凭证，或使用批量生成功能。
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped pending-stock-ins-table">
                            <thead class="table-dark">
                                <tr>
                                    <th width="5%">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th width="20%">入库单号</th>
                                    <th width="12%">入库日期</th>
                                    <th width="15%">供应商</th>
                                    <th width="20%">采购订单</th>
                                    <th width="12%" class="text-right">总金额</th>
                                    <th width="8%">状态</th>
                                    <th width="8%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stock_in in stock_ins %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="stock-in-checkbox" value="{{ stock_in.id }}">
                                    </td>
                                    <td style="word-break: break-all; white-space: normal;">
                                        <span class="stock-in-number">{{ stock_in.stock_in_number }}</span>
                                    </td>
                                    <td>{{ stock_in.stock_in_date.strftime('%Y-%m-%d') if stock_in.stock_in_date else '-' }}</td>
                                    <td>{{ stock_in.supplier.name if stock_in.supplier else '-' }}</td>
                                    <td style="word-break: break-all; white-space: normal;">
                                        {% if stock_in.purchase_order %}
                                            <a href="{{ url_for('purchase_order.view', id=stock_in.purchase_order.id) }}"
                                               target="_blank" class="purchase-order-link text-primary text-decoration-none">
                                                {{ stock_in.purchase_order.order_number }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-right amount-cell">
                                        <span class="text-success">¥{{ "%.2f"|format(stock_in.total_cost) }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">已财务确认</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('stock_in.view', id=stock_in.id) }}"
                                               class="btn btn-info btn-sm" title="查看入库单" target="_blank">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button type="button" class="btn btn-success btn-sm"
                                                    onclick="generateSingleVoucher({{ stock_in.id }}, '{{ stock_in.stock_in_number }}')"
                                                    title="生成凭证">
                                                <i class="fas fa-magic"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 批量操作 -->
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>批量操作</h6>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-primary" onclick="batchGenerateSelected()">
                                            <i class="fas fa-magic"></i> 为选中项生成凭证
                                        </button>
                                        <button type="button" class="btn btn-success" onclick="batchGenerateAll()">
                                            <i class="fas fa-cogs"></i> 为全部生成凭证
                                        </button>
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle"></i> 
                                            批量生成的凭证将自动审核通过。请确保会计科目设置正确。
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {% else %}
                    <div class="alert alert-warning text-center">
                        <i class="fas fa-exclamation-triangle"></i> 
                        <h5>暂无待生成凭证的入库单</h5>
                        <p class="mb-0">所有已财务确认的入库单都已生成财务凭证。</p>
                        <div class="mt-3">
                            <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-primary">
                                <i class="fas fa-arrow-left"></i> 返回凭证管理
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}
<script>
// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.stock-in-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// 生成单个凭证
function generateSingleVoucher(stockInId, stockInNumber) {
    if (!confirm(`确定要为入库单 ${stockInNumber} 生成财务凭证吗？`)) {
        return;
    }
    
    const btn = event.target.closest('button');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;
    
    fetch('{{ url_for("financial.generate_voucher_from_stock_in") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            stock_in_id: stockInId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`凭证生成成功！\n凭证号：${data.voucher_number}`);
            // 移除该行
            btn.closest('tr').remove();
            
            // 检查是否还有数据
            const tbody = document.querySelector('tbody');
            if (tbody.children.length === 0) {
                location.reload();
            }
        } else {
            alert('生成失败：' + data.message);
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    })
    .catch(error => {
        alert('网络错误：' + error.message);
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// 批量生成选中项
function batchGenerateSelected() {
    const selectedCheckboxes = document.querySelectorAll('.stock-in-checkbox:checked');
    
    if (selectedCheckboxes.length === 0) {
        alert('请先选择要生成凭证的入库单');
        return;
    }
    
    const stockInIds = Array.from(selectedCheckboxes).map(cb => parseInt(cb.value));
    
    if (!confirm(`确定要为选中的 ${stockInIds.length} 个入库单生成财务凭证吗？`)) {
        return;
    }
    
    batchGenerateVouchers(stockInIds);
}

// 批量生成全部
function batchGenerateAll() {
    const allCheckboxes = document.querySelectorAll('.stock-in-checkbox');
    
    if (allCheckboxes.length === 0) {
        alert('没有可生成凭证的入库单');
        return;
    }
    
    if (!confirm(`确定要为全部 ${allCheckboxes.length} 个入库单生成财务凭证吗？`)) {
        return;
    }
    
    const stockInIds = Array.from(allCheckboxes).map(cb => parseInt(cb.value));
    batchGenerateVouchers(stockInIds);
}

// 批量生成凭证
function batchGenerateVouchers(stockInIds) {
    // 显示进度提示
    const progressDiv = document.createElement('div');
    progressDiv.className = 'alert alert-info';
    progressDiv.innerHTML = `
        <i class="fas fa-spinner fa-spin"></i> 
        正在批量生成凭证，请稍候...（${stockInIds.length} 个入库单）
    `;
    document.querySelector('.card-body').insertBefore(progressDiv, document.querySelector('.table-responsive'));
    
    // 禁用所有按钮
    document.querySelectorAll('button').forEach(btn => btn.disabled = true);
    
    // 逐个生成凭证
    let successCount = 0;
    let failedCount = 0;
    let processedCount = 0;
    
    const processNext = () => {
        if (processedCount >= stockInIds.length) {
            // 全部处理完成
            progressDiv.className = 'alert alert-success';
            progressDiv.innerHTML = `
                <i class="fas fa-check-circle"></i> 
                批量生成完成！成功：${successCount} 个，失败：${failedCount} 个
            `;
            
            setTimeout(() => {
                location.reload();
            }, 2000);
            return;
        }
        
        const stockInId = stockInIds[processedCount];
        
        fetch('{{ url_for("financial.generate_voucher_from_stock_in") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                stock_in_id: stockInId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                successCount++;
            } else {
                failedCount++;
            }
            processedCount++;
            
            // 更新进度
            progressDiv.innerHTML = `
                <i class="fas fa-spinner fa-spin"></i> 
                正在批量生成凭证...（${processedCount}/${stockInIds.length}）
            `;
            
            // 处理下一个
            setTimeout(processNext, 500);
        })
        .catch(error => {
            failedCount++;
            processedCount++;
            
            // 处理下一个
            setTimeout(processNext, 500);
        });
    };
    
    // 开始处理
    processNext();
}
</script>
{% endblock %}
