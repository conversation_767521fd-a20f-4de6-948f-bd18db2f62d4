"""
会计科目管理路由
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.routes.financial import financial_bp
from app.models_financial import AccountingSubject
from app.forms.financial import AccountingSubjectForm
from app.utils.school_required import school_required
from app.utils.permissions import check_permission
from sqlalchemy import text
from datetime import datetime


@financial_bp.route('/accounting-subjects')
@login_required
@school_required
@check_permission('会计科目管理', 'view')
def accounting_subjects_index(user_area):
    """会计科目列表"""
    
    # 获取搜索参数
    keyword = request.args.get('keyword', '').strip()
    subject_type = request.args.get('subject_type', '').strip()
    
    # 读取系统会计科目和学校科目
    # 系统科目：is_system=1（所有学校共享）
    # 学校科目：area_id=当前学校（学校自定义）
    query = AccountingSubject.query.filter(
        db.or_(
            AccountingSubject.is_system == 1,  # 系统科目
            AccountingSubject.area_id == user_area.id  # 学校科目
        )
    )
    
    if keyword:
        query = query.filter(
            db.or_(
                AccountingSubject.code.like(f'%{keyword}%'),
                AccountingSubject.name.like(f'%{keyword}%')
            )
        )
    
    if subject_type:
        query = query.filter_by(subject_type=subject_type)
    
    # 分页
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('ITEMS_PER_PAGE', 20)
    
    subjects = query.order_by(AccountingSubject.code).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('financial/accounting_subjects/index.html',
                         subjects=subjects,
                         keyword=keyword,
                         subject_type=subject_type)


@financial_bp.route('/accounting-subjects/create', methods=['GET', 'POST'])
@login_required
@school_required
@check_permission('会计科目管理', 'create')
def create_accounting_subject(user_area):
    """创建会计科目"""
    form = AccountingSubjectForm()
    
    if form.validate_on_submit():
        try:
            # 检查科目编码是否已存在（在系统科目和学校科目中都要检查）
            existing = AccountingSubject.query.filter(
                db.or_(
                    AccountingSubject.is_system == 1,  # 系统科目
                    AccountingSubject.area_id == user_area.id  # 学校科目
                ),
                AccountingSubject.code == form.code.data
            ).first()

            if existing:
                flash('科目编码已存在，请使用其他编码', 'danger')
                return render_template('financial/accounting_subjects/form.html', form=form)
            
            # 使用原始SQL创建记录
            sql = text("""
                INSERT INTO accounting_subjects 
                (code, name, parent_id, level, subject_type, balance_direction, 
                 area_id, is_system, is_active, description, created_by)
                OUTPUT inserted.id
                VALUES 
                (:code, :name, :parent_id, :level, :subject_type, :balance_direction,
                 :area_id, :is_system, :is_active, :description, :created_by)
            """)
            
            # 计算科目级别
            level = 1
            if form.parent_id.data and form.parent_id.data > 0:
                parent = AccountingSubject.query.get(form.parent_id.data)
                if parent:
                    level = parent.level + 1

            params = {
                'code': form.code.data,
                'name': form.name.data,
                'parent_id': form.parent_id.data if form.parent_id.data > 0 else None,
                'level': level,
                'subject_type': form.subject_type.data,
                'balance_direction': form.balance_direction.data,
                'area_id': user_area.id,  # 学校科目使用学校的area_id
                'is_system': False,       # 学校科目标记为非系统科目
                'is_active': True,
                'description': form.description.data,
                'created_by': current_user.id
            }
            
            result = db.session.execute(sql, params)
            subject_id = result.fetchone()[0]
            db.session.commit()
            
            flash('会计科目创建成功', 'success')
            return redirect(url_for('financial.accounting_subjects_index'))
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建会计科目失败: {str(e)}")
            flash('创建失败，请重试', 'danger')
    
    return render_template('financial/accounting_subjects/form.html', form=form)


@financial_bp.route('/accounting-subjects/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@school_required
@check_permission('会计科目管理', 'edit')
def edit_accounting_subject(id, user_area):
    """编辑会计科目（只能编辑学校科目，不能编辑系统科目）"""
    subject = AccountingSubject.query.filter_by(
        id=id,
        area_id=user_area.id,
        is_system=False  # 只能编辑学校科目
    ).first_or_404()
    
    # 系统科目不允许编辑
    if subject.is_system:
        flash('系统科目不允许编辑', 'warning')
        return redirect(url_for('financial.accounting_subjects_index'))
    
    form = AccountingSubjectForm(obj=subject)
    
    if form.validate_on_submit():
        try:
            # 检查科目编码是否已存在（排除当前记录）
            existing = AccountingSubject.query.filter(
                db.or_(
                    AccountingSubject.is_system == 1,  # 系统科目
                    AccountingSubject.area_id == user_area.id  # 学校科目
                ),
                AccountingSubject.code == form.code.data,
                AccountingSubject.id != id
            ).first()
            
            if existing:
                flash('科目编码已存在，请使用其他编码', 'danger')
                return render_template('financial/accounting_subjects/form.html', 
                                     form=form, subject=subject)
            
            # 使用原始SQL更新记录
            sql = text("""
                UPDATE accounting_subjects
                SET code = :code,
                    name = :name,
                    parent_id = :parent_id,
                    level = :level,
                    subject_type = :subject_type,
                    balance_direction = :balance_direction,
                    description = :description
                WHERE id = :id AND area_id = :area_id
            """)
            
            # 计算科目级别
            level = 1
            if form.parent_id.data and form.parent_id.data > 0:
                parent = AccountingSubject.query.get(form.parent_id.data)
                if parent:
                    level = parent.level + 1
            
            params = {
                'code': form.code.data,
                'name': form.name.data,
                'parent_id': form.parent_id.data if form.parent_id.data > 0 else None,
                'level': level,
                'subject_type': form.subject_type.data,
                'balance_direction': form.balance_direction.data,
                'description': form.description.data,
                'id': id,
                'area_id': user_area.id
            }
            
            db.session.execute(sql, params)
            db.session.commit()
            
            flash('会计科目更新成功', 'success')
            return redirect(url_for('financial.accounting_subjects_index'))
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新会计科目失败: {str(e)}")
            flash('更新失败，请重试', 'danger')
    
    return render_template('financial/accounting_subjects/form.html', 
                         form=form, subject=subject)


@financial_bp.route('/accounting-subjects/<int:id>/delete', methods=['POST'])
@login_required
@school_required
@check_permission('会计科目管理', 'delete')
def delete_accounting_subject(id, user_area):
    """删除会计科目（只能删除学校科目，不能删除系统科目）"""
    subject = AccountingSubject.query.filter_by(
        id=id,
        area_id=user_area.id,
        is_system=False  # 只能删除学校科目
    ).first_or_404()
    
    # 系统科目不允许删除
    if subject.is_system:
        flash('系统科目不允许删除', 'warning')
        return redirect(url_for('financial.accounting_subjects_index'))
    
    # 检查是否有下级科目
    children = AccountingSubject.query.filter_by(parent_id=id).first()
    if children:
        flash('该科目有下级科目，不能删除', 'warning')
        return redirect(url_for('financial.accounting_subjects_index'))
    
    # 检查是否已被使用（这里可以添加更多检查）
    # TODO: 检查凭证明细中是否使用了该科目
    
    try:
        # 使用原始SQL删除
        sql = text("DELETE FROM accounting_subjects WHERE id = :id AND area_id = :area_id")
        db.session.execute(sql, {'id': id, 'area_id': user_area.id})
        db.session.commit()
        
        flash('会计科目删除成功', 'success')
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除会计科目失败: {str(e)}")
        flash('删除失败，该科目可能已被使用', 'danger')
    
    return redirect(url_for('financial.accounting_subjects_index'))


@financial_bp.route('/accounting-subjects/api')
@login_required
@school_required
def accounting_subjects_api(user_area):
    """会计科目API - 支持系统科目和学校科目"""

    subject_type = request.args.get('subject_type', '')
    keyword = request.args.get('keyword', '')
    include_system = request.args.get('include_system', 'true').lower() == 'true'

    # 构建查询条件
    conditions = [AccountingSubject.is_active == True]

    if include_system:
        # 包含系统科目和当前学校科目
        # 系统科目：is_system=1（不管area_id）
        # 学校科目：area_id=当前学校且is_system=0
        conditions.append(
            db.or_(
                AccountingSubject.is_system == 1,  # 所有系统科目
                db.and_(
                    AccountingSubject.area_id == user_area.id,
                    AccountingSubject.is_system == 0
                )  # 当前学校的自定义科目
            )
        )
    else:
        # 只包含当前学校科目
        conditions.append(
            db.and_(
                AccountingSubject.area_id == user_area.id,
                AccountingSubject.is_system == 0
            )
        )

    query = AccountingSubject.query.filter(db.and_(*conditions))

    if subject_type:
        query = query.filter_by(subject_type=subject_type)

    if keyword:
        query = query.filter(
            db.or_(
                AccountingSubject.code.like(f'%{keyword}%'),
                AccountingSubject.name.like(f'%{keyword}%')
            )
        )

    subjects = query.order_by(AccountingSubject.code).all()

    return jsonify([subject.to_dict() for subject in subjects])


@financial_bp.route('/accounting-subjects/types')
@login_required
@school_required
def accounting_subject_types(user_area):
    """获取会计科目类型列表 - 合并系统科目和学校科目"""

    # 获取系统区域ID（最小的区域ID）
    from app.models import AdministrativeArea
    system_area = AdministrativeArea.query.order_by(AdministrativeArea.id).first()
    system_area_id = system_area.id if system_area else 0

    # 查询所有有效的科目类型，包括系统科目和学校科目
    # 使用 db.or_ 结合系统科目和学校科目的查询条件
    types_query = db.session.query(AccountingSubject.subject_type).filter(
        db.or_(
            db.and_(
                AccountingSubject.area_id == system_area_id,
                AccountingSubject.is_system == True
            ),
            db.and_(
                AccountingSubject.area_id == user_area.id,
                AccountingSubject.is_system == False
            )
        ),
        AccountingSubject.is_active == True
    ).distinct()
    
    available_types = [t[0] for t in types_query.all()]

    # 预定义科目类型顺序
    type_order = ['资产', '负债', '所有者权益', '净资产', '收入', '费用']
    
    # 按照预定义顺序排序，并确保只包含实际存在的类型
    ordered_types = [t for t in type_order if t in available_types]

    return jsonify([{
        'value': type_name,
        'label': f'{type_name}类科目'
    } for type_name in ordered_types])


@financial_bp.route('/accounting-subjects/by-type')
@login_required
@school_required
def accounting_subjects_by_type(user_area):
    """根据科目类型获取一级科目"""

    subject_type = request.args.get('subject_type', '')
    if not subject_type:
        return jsonify([])

    # 获取系统区域ID（最小的区域ID）
    from app.models import AdministrativeArea
    system_area = AdministrativeArea.query.order_by(AdministrativeArea.id).first()
    system_area_id = system_area.id if system_area else 0

    # 查询一级科目（level=1），同时包含系统科目和学校科目
    subjects = AccountingSubject.query.filter(
        db.or_(
            # 系统科目
            db.and_(
                AccountingSubject.area_id == system_area_id,
                AccountingSubject.is_system == True
            ),
            # 学校科目
            db.and_(
                AccountingSubject.area_id == user_area.id,
                AccountingSubject.is_system == False
            )
        ),
        AccountingSubject.subject_type == subject_type,
        AccountingSubject.level == 1,
        AccountingSubject.is_active == True
    ).order_by(AccountingSubject.code).all()

    return jsonify([{
        'id': subject.id,
        'code': subject.code,
        'name': subject.name,
        'display_name': f'{subject.code} - {subject.name}',
        'is_system': subject.is_system
    } for subject in subjects])


@financial_bp.route('/accounting-subjects/by-parent')
@login_required
@school_required
def accounting_subjects_by_parent(user_area):
    """根据上级科目获取下级科目"""

    parent_id = request.args.get('parent_id', type=int)
    if not parent_id:
        return jsonify([])

    # 获取系统区域ID（最小的区域ID）
    from app.models import AdministrativeArea
    system_area = AdministrativeArea.query.order_by(AdministrativeArea.id).first()
    system_area_id = system_area.id if system_area else 0

    # 获取父级科目信息
    parent_subject = AccountingSubject.query.get(parent_id)
    if not parent_subject:
        return jsonify([])

    # 查询下级科目，同时包含系统科目和学校科目
    subjects = AccountingSubject.query.filter(
        db.or_(
            # 系统科目
            db.and_(
                AccountingSubject.area_id == system_area_id,
                AccountingSubject.is_system == True
            ),
            # 学校科目
            db.and_(
                AccountingSubject.area_id == user_area.id,
                AccountingSubject.is_system == False
            )
        ),
        AccountingSubject.parent_id == parent_id,
        AccountingSubject.is_active == True
    ).order_by(AccountingSubject.code).all()

    return jsonify([{
        'id': subject.id,
        'code': subject.code,
        'name': subject.name,
        'display_name': f'{subject.code} - {subject.name}',
        'is_system': subject.is_system
    } for subject in subjects])


@financial_bp.route('/accounting-subjects/init-system', methods=['POST'])
@login_required
@school_required
@check_permission('会计科目管理', 'create')
def init_system_subjects(user_area):
    """初始化系统会计科目"""
    try:
        # 检查是否已有系统科目
        existing_count = AccountingSubject.query.filter_by(area_id=1, is_system=True).count()
        if existing_count > 0:
            return jsonify({
                'success': False,
                'message': f'系统会计科目已存在（{existing_count}个），如需重新初始化请先清理'
            })

        # 标准会计科目数据
        system_subjects = [
            # 资产类
            {'code': '1001', 'name': '库存现金', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1002', 'name': '银行存款', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1012', 'name': '其他货币资金', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1121', 'name': '应收账款', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1131', 'name': '预付账款', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1401', 'name': '库存商品', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1402', 'name': '原材料', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1601', 'name': '固定资产', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1602', 'name': '累计折旧', 'subject_type': '资产', 'balance_direction': '贷方', 'level': 1},

            # 负债类
            {'code': '2001', 'name': '短期借款', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1},
            {'code': '2201', 'name': '应付账款', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1},
            {'code': '2202', 'name': '预收账款', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1},
            {'code': '2211', 'name': '应付职工薪酬', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1},
            {'code': '2221', 'name': '应交税费', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1},

            # 所有者权益类
            {'code': '3001', 'name': '实收资本', 'subject_type': '所有者权益', 'balance_direction': '贷方', 'level': 1},
            {'code': '3002', 'name': '资本公积', 'subject_type': '所有者权益', 'balance_direction': '贷方', 'level': 1},
            {'code': '3101', 'name': '盈余公积', 'subject_type': '所有者权益', 'balance_direction': '贷方', 'level': 1},
            {'code': '3103', 'name': '本年利润', 'subject_type': '所有者权益', 'balance_direction': '贷方', 'level': 1},
            {'code': '3104', 'name': '利润分配', 'subject_type': '所有者权益', 'balance_direction': '贷方', 'level': 1},

            # 收入类
            {'code': '6001', 'name': '主营业务收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1},
            {'code': '6051', 'name': '其他业务收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1},
            {'code': '6111', 'name': '投资收益', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1},
            {'code': '6301', 'name': '营业外收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1},

            # 费用类
            {'code': '6401', 'name': '主营业务成本', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1},
            {'code': '6402', 'name': '其他业务成本', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1},
            {'code': '6601', 'name': '销售费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1},
            {'code': '6602', 'name': '管理费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1},
            {'code': '6603', 'name': '财务费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1},
            {'code': '6701', 'name': '营业外支出', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1},
            {'code': '6801', 'name': '所得税费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1},
        ]

        # 批量插入系统科目
        created_count = 0
        for subject_data in system_subjects:
            sql = text("""
                INSERT INTO accounting_subjects
                (code, name, parent_id, level, subject_type, balance_direction,
                 area_id, is_system, is_active, description, created_by)
                VALUES
                (:code, :name, :parent_id, :level, :subject_type, :balance_direction,
                 :area_id, :is_system, :is_active, :description, :created_by)
            """)

            params = {
                'code': subject_data['code'],
                'name': subject_data['name'],
                'parent_id': None,
                'level': subject_data['level'],
                'subject_type': subject_data['subject_type'],
                'balance_direction': subject_data['balance_direction'],
                'area_id': 1,  # 系统科目使用系统区域ID 1
                'is_system': True,
                'is_active': True,
                'description': f"系统标准会计科目 - {subject_data['name']}",
                'created_by': current_user.id
            }

            db.session.execute(sql, params)
            created_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'成功创建 {created_count} 个系统会计科目'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"初始化系统会计科目失败: {str(e)}")
        return jsonify({'success': False, 'message': f'初始化失败: {str(e)}'})


@financial_bp.route('/accounting-subjects/copy-from-system', methods=['POST'])
@login_required
@school_required
@check_permission('会计科目管理', 'create')
def copy_from_system_subjects(user_area):
    """从系统科目复制到当前学校"""
    try:
        # 检查当前学校是否已有科目
        existing_count = AccountingSubject.query.filter_by(area_id=user_area.id).count()
        if existing_count > 0:
            return jsonify({
                'success': False,
                'message': f'当前学校已有 {existing_count} 个会计科目，请先清理后再复制'
            })

        # 获取系统科目
        system_subjects = AccountingSubject.query.filter_by(area_id=1, is_system=True, is_active=True).all()
        if not system_subjects:
            return jsonify({
                'success': False,
                'message': '没有找到系统会计科目，请先初始化系统科目'
            })

        # 复制系统科目到当前学校
        copied_count = 0
        for system_subject in system_subjects:
            sql = text("""
                INSERT INTO accounting_subjects
                (code, name, parent_id, level, subject_type, balance_direction,
                 area_id, is_system, is_active, description, created_by)
                VALUES
                (:code, :name, :parent_id, :level, :subject_type, :balance_direction,
                 :area_id, :is_system, :is_active, :description, :created_by)
            """)

            params = {
                'code': system_subject.code,
                'name': system_subject.name,
                'parent_id': system_subject.parent_id,
                'level': system_subject.level,
                'subject_type': system_subject.subject_type,
                'balance_direction': system_subject.balance_direction,
                'area_id': user_area.id,  # 设置为当前学校
                'is_system': False,  # 标记为学校科目
                'is_active': True,
                'description': f"基于系统科目复制 - {system_subject.name}",
                'created_by': current_user.id
            }

            db.session.execute(sql, params)
            copied_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'成功为学校 {user_area.name} 复制了 {copied_count} 个会计科目'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"复制系统会计科目失败: {str(e)}")
        return jsonify({'success': False, 'message': f'复制失败: {str(e)}'})


@financial_bp.route('/accounting-subjects/force-init-system', methods=['POST'])
@login_required
@school_required
@check_permission('会计科目管理', 'create')
def force_init_system_subjects(user_area):
    """强制初始化系统会计科目（会先清理现有的）"""
    try:
        # 先删除现有的系统科目
        existing_count = AccountingSubject.query.filter_by(area_id=1, is_system=True).count()
        if existing_count > 0:
            # 使用原生SQL删除系统科目
            sql = text("DELETE FROM accounting_subjects WHERE area_id = 1 AND is_system = 1")
            db.session.execute(sql)
            current_app.logger.info(f"删除了 {existing_count} 个现有系统科目")

        # 标准会计科目数据
        system_subjects = [
            # 资产类
            {'code': '1001', 'name': '库存现金', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1002', 'name': '银行存款', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1012', 'name': '其他货币资金', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1121', 'name': '应收账款', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1131', 'name': '预付账款', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1401', 'name': '库存商品', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1402', 'name': '原材料', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1601', 'name': '固定资产', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1602', 'name': '累计折旧', 'subject_type': '资产', 'balance_direction': '贷方', 'level': 1},

            # 负债类
            {'code': '2001', 'name': '短期借款', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1},
            {'code': '2201', 'name': '应付账款', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1},
            {'code': '2202', 'name': '预收账款', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1},
            {'code': '2211', 'name': '应付职工薪酬', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1},
            {'code': '2221', 'name': '应交税费', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1},

            # 所有者权益类
            {'code': '3001', 'name': '实收资本', 'subject_type': '所有者权益', 'balance_direction': '贷方', 'level': 1},
            {'code': '3002', 'name': '资本公积', 'subject_type': '所有者权益', 'balance_direction': '贷方', 'level': 1},
            {'code': '3101', 'name': '盈余公积', 'subject_type': '所有者权益', 'balance_direction': '贷方', 'level': 1},
            {'code': '3103', 'name': '本年利润', 'subject_type': '所有者权益', 'balance_direction': '贷方', 'level': 1},
            {'code': '3104', 'name': '利润分配', 'subject_type': '所有者权益', 'balance_direction': '贷方', 'level': 1},

            # 收入类
            {'code': '6001', 'name': '主营业务收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1},
            {'code': '6051', 'name': '其他业务收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1},
            {'code': '6111', 'name': '投资收益', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1},
            {'code': '6301', 'name': '营业外收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1},

            # 费用类
            {'code': '6401', 'name': '主营业务成本', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1},
            {'code': '6402', 'name': '其他业务成本', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1},
            {'code': '6601', 'name': '销售费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1},
            {'code': '6602', 'name': '管理费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1},
            {'code': '6603', 'name': '财务费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1},
            {'code': '6701', 'name': '营业外支出', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1},
            {'code': '6801', 'name': '所得税费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1},
        ]

        # 批量插入系统科目
        created_count = 0
        for subject_data in system_subjects:
            sql = text("""
                INSERT INTO accounting_subjects
                (code, name, parent_id, level, subject_type, balance_direction,
                 area_id, is_system, is_active, description, created_by)
                VALUES
                (:code, :name, :parent_id, :level, :subject_type, :balance_direction,
                 :area_id, :is_system, :is_active, :description, :created_by)
            """)

            params = {
                'code': subject_data['code'],
                'name': subject_data['name'],
                'parent_id': None,
                'level': subject_data['level'],
                'subject_type': subject_data['subject_type'],
                'balance_direction': subject_data['balance_direction'],
                'area_id': 1,  # 系统科目使用系统区域ID 1
                'is_system': True,
                'is_active': True,
                'description': f"系统标准会计科目 - {subject_data['name']}",
                'created_by': current_user.id
            }

            db.session.execute(sql, params)
            created_count += 1

        db.session.commit()

        message = f'强制初始化完成！'
        if existing_count > 0:
            message += f'删除了 {existing_count} 个旧科目，'
        message += f'创建了 {created_count} 个新的系统会计科目'

        return jsonify({
            'success': True,
            'message': message
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"强制初始化系统会计科目失败: {str(e)}")
        return jsonify({'success': False, 'message': f'强制初始化失败: {str(e)}'})


@financial_bp.route('/accounting-subjects/clear-school', methods=['POST'])
@login_required
@school_required
@check_permission('会计科目管理', 'delete')
def clear_school_subjects(user_area):
    """清空当前学校的会计科目"""
    try:
        # 检查是否有凭证使用了这些科目
        from app.models_financial import VoucherDetail
        used_subjects = db.session.query(VoucherDetail.subject_id).join(
            AccountingSubject, VoucherDetail.subject_id == AccountingSubject.id
        ).filter(AccountingSubject.area_id == user_area.id).distinct().all()

        if used_subjects:
            return jsonify({
                'success': False,
                'message': f'有 {len(used_subjects)} 个科目已被凭证使用，无法清空'
            })

        # 删除当前学校的所有科目（非系统科目）
        deleted_count = AccountingSubject.query.filter_by(
            area_id=user_area.id,
            is_system=False
        ).count()

        if deleted_count == 0:
            return jsonify({
                'success': False,
                'message': '当前学校没有科目需要清空'
            })

        # 使用原生SQL删除
        sql = text("DELETE FROM accounting_subjects WHERE area_id = :area_id AND is_system = 0")
        db.session.execute(sql, {'area_id': user_area.id})
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'成功清空 {deleted_count} 个学校会计科目'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"清空学校会计科目失败: {str(e)}")
        return jsonify({'success': False, 'message': f'清空失败: {str(e)}'})


@financial_bp.route('/accounting-subjects/clean-duplicates', methods=['POST'])
@login_required
@school_required
@check_permission('会计科目管理', 'delete')
def clean_duplicate_subjects(user_area):
    """清理重复的会计科目"""
    try:
        # 检查是否有凭证使用了这些科目
        from app.models_financial import VoucherDetail
        used_subjects = db.session.query(VoucherDetail.subject_id).distinct().all()
        used_subject_ids = [s[0] for s in used_subjects] if used_subjects else []

        if used_subject_ids:
            return jsonify({
                'success': False,
                'message': f'有 {len(used_subject_ids)} 个科目已被凭证使用，无法清理重复科目'
            })

        # 确保系统区域存在 - 使用现有的第一个区域作为系统区域
        from app.models import AdministrativeArea
        system_area = AdministrativeArea.query.order_by(AdministrativeArea.id).first()
        if not system_area:
            return jsonify({'success': False, 'message': '没有找到任何区域，无法创建系统科目'})

        system_area_id = system_area.id
        current_app.logger.info(f"使用区域 {system_area_id} ({system_area.name}) 作为系统区域")

        # 删除所有现有科目（因为数据结构有问题）
        total_before = AccountingSubject.query.count()

        # 使用原生SQL删除所有科目
        sql = text("DELETE FROM accounting_subjects")
        db.session.execute(sql)

        current_app.logger.info(f"删除了 {total_before} 个有问题的科目")

        # 重新创建正确的系统科目
        system_subjects = [
            # 资产类
            {'code': '1001', 'name': '库存现金', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1002', 'name': '银行存款', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1012', 'name': '其他货币资金', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1121', 'name': '应收账款', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1131', 'name': '预付账款', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1401', 'name': '库存商品', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1402', 'name': '原材料', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1601', 'name': '固定资产', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1},
            {'code': '1602', 'name': '累计折旧', 'subject_type': '资产', 'balance_direction': '贷方', 'level': 1},

            # 负债类
            {'code': '2001', 'name': '短期借款', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1},
            {'code': '2201', 'name': '应付账款', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1},
            {'code': '2202', 'name': '预收账款', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1},
            {'code': '2211', 'name': '应付职工薪酬', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1},
            {'code': '2221', 'name': '应交税费', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1},

            # 所有者权益类
            {'code': '3001', 'name': '实收资本', 'subject_type': '所有者权益', 'balance_direction': '贷方', 'level': 1},
            {'code': '3002', 'name': '资本公积', 'subject_type': '所有者权益', 'balance_direction': '贷方', 'level': 1},
            {'code': '3101', 'name': '盈余公积', 'subject_type': '所有者权益', 'balance_direction': '贷方', 'level': 1},
            {'code': '3103', 'name': '本年利润', 'subject_type': '所有者权益', 'balance_direction': '贷方', 'level': 1},
            {'code': '3104', 'name': '利润分配', 'subject_type': '所有者权益', 'balance_direction': '贷方', 'level': 1},

            # 收入类
            {'code': '6001', 'name': '主营业务收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1},
            {'code': '6001001', 'name': '学生餐费收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 2, 'description': '按实际收取金额确认的学生餐费'},
            {'code': '6001002', 'name': '教师餐费收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 2, 'description': '校内教职工就餐缴费'},
            {'code': '6001003', 'name': '外来人员餐费收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 2, 'description': '对外销售糕点、饮料的收入'},
            {'code': '6051', 'name': '其他业务收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1, 'description': '对外销售糕点、饮料的收入'},
            {'code': '6111', 'name': '投资收益', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1, 'description': '投资收益'},
            {'code': '6301', 'name': '营业外收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1, 'description': '营业外收入'},

            # 费用类
            {'code': '6401', 'name': '主营业务成本', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1, 'description': '核算食材采购支出'},
            {'code': '6401001', 'name': '食材成本', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'description': '米面粮油、蔬菜、肉类、调味品等'},
            {'code': '6402', 'name': '管理费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1, 'description': '核算食堂管理人员工资、行政办公费由学校统一支付的分摊'},
            {'code': '6402001', 'name': '职工薪酬', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'description': '核算食堂员工工资、社保、公积金'},
            {'code': '6402002', 'name': '水电费', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'description': '核算厨房设备使用的水费、电费、燃气费'},
            {'code': '6402003', 'name': '燃气费', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'description': '核算厨房设备使用的燃气费'},
            {'code': '6402004', 'name': '设备维护费', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'description': '核算厨房设备维修、管道疏通等支出'},
            {'code': '6603', 'name': '财务费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1, 'description': '核算财务费用'},
            {'code': '6701', 'name': '营业外支出', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1, 'description': '核算营业外支出'},
            {'code': '6801', 'name': '所得税费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1, 'description': '核算所得税费用'},
        ]

        # 批量插入正确的系统科目
        created_count = 0
        for subject_data in system_subjects:
            sql = text("""
                INSERT INTO accounting_subjects
                (code, name, parent_id, level, subject_type, balance_direction,
                 area_id, is_system, is_active, description, created_by)
                VALUES
                (:code, :name, :parent_id, :level, :subject_type, :balance_direction,
                 :area_id, :is_system, :is_active, :description, :created_by)
            """)

            params = {
                'code': subject_data['code'],
                'name': subject_data['name'],
                'parent_id': None,
                'level': subject_data['level'],
                'subject_type': subject_data['subject_type'],
                'balance_direction': subject_data['balance_direction'],
                'area_id': system_area_id,  # 系统科目使用系统区域ID
                'is_system': True,
                'is_active': True,
                'description': subject_data.get('description', f"系统标准会计科目 - {subject_data['name']}"),
                'created_by': current_user.id
            }

            db.session.execute(sql, params)
            created_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'清理完成！删除了 {total_before} 个重复科目，重新创建了 {created_count} 个标准系统科目'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"清理重复会计科目失败: {str(e)}")
        return jsonify({'success': False, 'message': f'清理失败: {str(e)}'})


@financial_bp.route('/accounting-subjects/init-canteen-system', methods=['POST'])
@login_required
@school_required
@check_permission('会计科目管理', 'create')
def init_canteen_system_subjects(user_area):
    """初始化学校食堂专用会计科目体系"""
    try:
        # 检查是否已有系统科目
        existing_count = AccountingSubject.query.filter_by(area_id=1, is_system=True).count()
        if existing_count > 0:
            return jsonify({
                'success': False,
                'message': f'系统会计科目已存在（{existing_count}个），如需重新初始化请先清理'
            })

        # 学校食堂专用会计科目体系
        canteen_subjects = [
            # 一、资产类科目
            # （1）流动资产
            {'code': '1001', 'name': '库存现金', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'description': '核算食堂日常零星现金收支（如学生充值、找零、临时采购）'},
            {'code': '1002', 'name': '银行存款', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'description': '核算食堂银行账户资金（如学生餐费预存专户、收入结算户）'},
            {'code': '1003', 'name': '其他货币资金', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'description': '核算校园卡预存资金（若食堂与校园卡系统关联）'},
            {'code': '1101', 'name': '应收票据', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'description': '核算食堂收到供应商开具的商业汇票'},
            {'code': '1121', 'name': '应收账款', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'description': '核算学生餐费预收后未消费的余额'},
            {'code': '1122', 'name': '其他应收款', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'description': '核算食堂临时借用的备用金（采购员预支现金）'},
            {'code': '1131', 'name': '预付账款', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'description': '预付给食材供应商的货款'},

            # 库存物品
            {'code': '1401', 'name': '库存物品', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'description': '核算食堂各类库存物品'},
            {'code': '140101', 'name': '原材料', 'subject_type': '资产', 'balance_direction': '借方', 'level': 2, 'description': '米面粮油、蔬菜、肉类、调味品等'},
            {'code': '14010101', 'name': '米面粮油', 'subject_type': '资产', 'balance_direction': '借方', 'level': 3, 'description': '大米、面粉、食用油等主食材料'},
            {'code': '14010102', 'name': '蔬菜类', 'subject_type': '资产', 'balance_direction': '借方', 'level': 3, 'description': '各类新鲜蔬菜'},
            {'code': '14010103', 'name': '肉类', 'subject_type': '资产', 'balance_direction': '借方', 'level': 3, 'description': '猪肉、牛肉、鸡肉、鱼类等'},
            {'code': '14010104', 'name': '调味品', 'subject_type': '资产', 'balance_direction': '借方', 'level': 3, 'description': '盐、糖、酱油、醋等调味料'},

            {'code': '140102', 'name': '低值易耗品', 'subject_type': '资产', 'balance_direction': '借方', 'level': 2, 'description': '餐具、清洁用品、包装材料等'},
            {'code': '14010201', 'name': '餐具', 'subject_type': '资产', 'balance_direction': '借方', 'level': 3, 'description': '碗筷、盘子、勺子等餐具'},
            {'code': '14010202', 'name': '清洁用品', 'subject_type': '资产', 'balance_direction': '借方', 'level': 3, 'description': '洗洁精、抹布、消毒液等'},
            {'code': '14010203', 'name': '包装材料', 'subject_type': '资产', 'balance_direction': '借方', 'level': 3, 'description': '打包盒、塑料袋等包装用品'},

            {'code': '140103', 'name': '物料用品', 'subject_type': '资产', 'balance_direction': '借方', 'level': 2, 'description': '一次性用品、印刷品等'},

            {'code': '1501', 'name': '待摊费用', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'description': '核算已支付但需分摊的费用（如季度食堂保险费）'},

            # （2）固定资产
            {'code': '1601', 'name': '固定资产', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'description': '核算食堂固定资产'},
            {'code': '160101', 'name': '厨房设备', 'subject_type': '资产', 'balance_direction': '借方', 'level': 2, 'description': '灶具、蒸箱、冰箱、消毒柜、油烟机'},
            {'code': '160102', 'name': '餐饮家具', 'subject_type': '资产', 'balance_direction': '借方', 'level': 2, 'description': '餐桌椅、餐具柜、收银台'},
            {'code': '160103', 'name': '其他设备', 'subject_type': '资产', 'balance_direction': '借方', 'level': 2, 'description': '空调、饮水机、电子秤'},

            {'code': '1602', 'name': '固定资产累计折旧', 'subject_type': '资产', 'balance_direction': '贷方', 'level': 1, 'description': '核算固定资产折旧的累计金额'},
            {'code': '1701', 'name': '在建工程', 'subject_type': '资产', 'balance_direction': '借方', 'level': 1, 'description': '核算食堂改造、设备安装等在建项目'},

            # 二、负债类科目
            {'code': '2001', 'name': '短期借款', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1, 'description': '食堂临时资金周转向学校或其他机构借入的款项'},
            {'code': '2101', 'name': '应付票据', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1, 'description': '核算食堂开具的商业汇票'},
            {'code': '2201', 'name': '应付账款', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1, 'description': '核算应付未付的食材、调料、设备货款'},
            {'code': '2211', 'name': '应付职工薪酬', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1, 'description': '核算食堂员工工资、社保、公积金等'},

            {'code': '2221', 'name': '其他应付款', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1, 'description': '核算其他应付款项'},
            {'code': '222101', 'name': '学生餐费预收余额', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 2, 'description': '未消费的预存餐费'},
            {'code': '222102', 'name': '押金', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 2, 'description': '餐具押金、设备租赁押金'},
            {'code': '222103', 'name': '应付水电费', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 2, 'description': '已使用但未缴纳的水电燃气费'},

            {'code': '2231', 'name': '应交税费', 'subject_type': '负债', 'balance_direction': '贷方', 'level': 1, 'description': '核算食堂对外经营产生的增值税、城建税等'},

            # 三、净资产类科目
            {'code': '3001', 'name': '累计盈余', 'subject_type': '净资产', 'balance_direction': '贷方', 'level': 1, 'description': '核算食堂历年经营结余（收入扣除成本、费用后的净额）'},
            {'code': '3101', 'name': '专用基金', 'subject_type': '净资产', 'balance_direction': '贷方', 'level': 1, 'description': '核算专项资金'},
            {'code': '310101', 'name': '学生伙食补助基金', 'subject_type': '净资产', 'balance_direction': '贷方', 'level': 2, 'description': '学校提取的专项资金，用于补贴困难学生餐费'},

            # 四、收入类科目
            {'code': '4001', 'name': '事业收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1, 'description': '核算食堂各项收入'},
            {'code': '400101', 'name': '食堂收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 2, 'description': '食堂经营收入'},
            {'code': '40010101', 'name': '学生餐费收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 3, 'description': '按实际收取金额确认的学生餐费'},
            {'code': '40010102', 'name': '教职工餐费收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 3, 'description': '校内教职工就餐缴费'},
            {'code': '40010103', 'name': '其他经营收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 3, 'description': '对外销售糕点、饮料的收入'},

            {'code': '4002', 'name': '上级补助收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1, 'description': '学校拨入的食堂专项补贴'},
            {'code': '400201', 'name': '设备购置补贴', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 2, 'description': '学校拨入的设备购置专项补贴'},
            {'code': '400202', 'name': '学生伙食补助', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 2, 'description': '学校拨入的学生伙食专项补助'},

            {'code': '4003', 'name': '附属单位上缴收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1, 'description': '若食堂独立核算，定期将部分结余上缴学校财务'},
            {'code': '4004', 'name': '其他收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 1, 'description': '其他各项收入'},
            {'code': '400401', 'name': '废旧物资处置收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 2, 'description': '废油、废纸箱销售收入'},
            {'code': '400402', 'name': '捐赠收入', 'subject_type': '收入', 'balance_direction': '贷方', 'level': 2, 'description': '企业赞助食材、设备等捐赠收入'},

            # 五、费用类科目
            # （1）业务活动费用（食堂直接成本与费用）
            {'code': '5001', 'name': '业务活动费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1, 'description': '核算食堂直接成本与费用'},
            {'code': '500101', 'name': '原材料成本', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'description': '核算食材采购支出'},
            {'code': '50010101', 'name': '米面粮油成本', 'subject_type': '费用', 'balance_direction': '借方', 'level': 3, 'description': '大米、面粉、食用油等主食材料成本'},
            {'code': '50010102', 'name': '蔬菜成本', 'subject_type': '费用', 'balance_direction': '借方', 'level': 3, 'description': '各类新鲜蔬菜成本'},
            {'code': '50010103', 'name': '肉类成本', 'subject_type': '费用', 'balance_direction': '借方', 'level': 3, 'description': '猪肉、牛肉、鸡肉、鱼类等成本'},
            {'code': '50010104', 'name': '调味品成本', 'subject_type': '费用', 'balance_direction': '借方', 'level': 3, 'description': '盐、糖、酱油、醋等调味料成本'},

            {'code': '500102', 'name': '人工成本', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'description': '食堂员工工资、社保、公积金'},
            {'code': '500103', 'name': '水电燃气费', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'description': '厨房设备使用的水费、电费、燃气费'},
            {'code': '500104', 'name': '固定资产折旧费', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'description': '厨房设备、家具的折旧费用'},
            {'code': '500105', 'name': '低值易耗品摊销', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'description': '餐具、清洁用品等低值易耗品的摊销费用'},
            {'code': '500106', 'name': '维修费', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'description': '厨房设备维修、管道疏通等支出'},
            {'code': '500107', 'name': '其他商品和服务费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'description': '垃圾清运费、清洁服务费、审计费、办公用品费等'},

            # （2）单位管理费用（间接费用分摊）
            {'code': '5002', 'name': '管理费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 1, 'description': '核算间接费用分摊'},
            {'code': '500201', 'name': '分摊费用', 'subject_type': '费用', 'balance_direction': '借方', 'level': 2, 'description': '食堂管理人员工资、行政办公费由学校统一支付的分摊'},

            # 六、预算会计科目（政府会计制度新增）
            {'code': '6001', 'name': '预算收入', 'subject_type': '预算收入', 'balance_direction': '贷方', 'level': 1, 'description': '核算食堂预算年度内预计收入'},
            {'code': '600101', 'name': '食堂预算收入', 'subject_type': '预算收入', 'balance_direction': '贷方', 'level': 2, 'description': '用于预算编制与执行分析'},

            {'code': '7001', 'name': '预算支出', 'subject_type': '预算支出', 'balance_direction': '借方', 'level': 1, 'description': '核算食堂预算年度内预计支出'},
            {'code': '700101', 'name': '食堂预算支出', 'subject_type': '预算支出', 'balance_direction': '借方', 'level': 2, 'description': '食材采购预算、人工成本预算等'},
        ]

        # 批量插入系统科目
        created_count = 0
        for subject_data in canteen_subjects:
            sql = text("""
                INSERT INTO accounting_subjects
                (code, name, parent_id, level, subject_type, balance_direction,
                 area_id, is_system, is_active, description, created_by)
                VALUES
                (:code, :name, :parent_id, :level, :subject_type, :balance_direction,
                 :area_id, :is_system, :is_active, :description, :created_by)
            """)

            params = {
                'code': subject_data['code'],
                'name': subject_data['name'],
                'parent_id': None,
                'level': subject_data['level'],
                'subject_type': subject_data['subject_type'],
                'balance_direction': subject_data['balance_direction'],
                'area_id': 1,  # 系统科目使用系统区域ID 1
                'is_system': True,
                'is_active': True,
                'description': subject_data.get('description', f"学校食堂专用会计科目 - {subject_data['name']}"),
                'created_by': current_user.id
            }

            db.session.execute(sql, params)
            created_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'成功创建 {created_count} 个学校食堂专用会计科目（第一部分：资产、负债类）'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"初始化学校食堂会计科目失败: {str(e)}")
        return jsonify({'success': False, 'message': f'初始化失败: {str(e)}'})



