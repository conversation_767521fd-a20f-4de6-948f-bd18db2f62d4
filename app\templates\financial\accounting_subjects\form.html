{% extends "financial/base.html" %}

{% block page_title %}
{% if subject %}编辑会计科目{% else %}新增会计科目{% endif %}
{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{{ url_for('financial.accounting_subjects_index') }}">会计科目管理</a></li>
<li class="breadcrumb-item active">
    {% if subject %}编辑科目{% else %}新增科目{% endif %}
</li>
{% endblock %}

{% block page_actions %}
<div class="financial-actions">
    <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> 返回列表
    </a>
</div>
{% endblock %}

{% block financial_content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="financial-card">
            <div class="financial-card-header">
                <i class="fas fa-{% if subject %}edit{% else %}plus{% endif %}"></i>
                {% if subject %}编辑会计科目{% else %}新增会计科目{% endif %}
            </div>
            <div class="financial-card-body">
                <form method="POST" class="financial-form">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.code.label(class="form-label") }}
                                {{ form.code(class="form-control" + (" is-invalid" if form.code.errors else "")) }}
                                {% if form.code.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.code.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    科目编码用于唯一标识会计科目，建议使用数字编码
                                </small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                {% if form.name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.name.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.subject_type.label(class="form-label") }}
                                {{ form.subject_type(class="form-control" + (" is-invalid" if form.subject_type.errors else ""), id="subject_type") }}
                                {% if form.subject_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.subject_type.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.balance_direction.label(class="form-label") }}
                                {{ form.balance_direction(class="form-control" + (" is-invalid" if form.balance_direction.errors else ""), id="balance_direction") }}
                                {% if form.balance_direction.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.balance_direction.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted" id="balance_direction_help">
                                    余额方向将根据科目类型自动设置
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 会计科目树型结构选择 -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-sitemap"></i> 选择上级科目
                            <small class="text-muted">（可选，不选则创建一级科目）</small>
                        </label>

                        <!-- 科目类型过滤 -->
                        <div class="mb-3">
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-secondary active" data-type="">全部</button>
                                <button type="button" class="btn btn-outline-primary" data-type="资产">资产</button>
                                <button type="button" class="btn btn-outline-success" data-type="负债">负债</button>
                                <button type="button" class="btn btn-outline-info" data-type="所有者权益">权益</button>
                                <button type="button" class="btn btn-outline-warning" data-type="收入">收入</button>
                                <button type="button" class="btn btn-outline-danger" data-type="费用">费用</button>
                            </div>
                        </div>

                        <!-- 科目树型结构 -->
                        <div class="subject-tree-container" style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px; padding: 10px;">
                            <div id="subject-tree">
                                <div class="text-center text-muted py-3">
                                    <i class="fas fa-spinner fa-spin"></i> 加载科目数据中...
                                </div>
                            </div>
                        </div>

                        <!-- 选中的科目信息 -->
                        <div id="selected-subject-info" class="mt-2" style="display: none;">
                            <div class="alert alert-info">
                                <strong>选中科目：</strong>
                                <span id="selected-subject-text"></span>
                                <button type="button" class="btn btn-sm btn-outline-secondary ml-2" onclick="clearSelection()">
                                    <i class="fas fa-times"></i> 清除选择
                                </button>
                            </div>
                        </div>

                        <!-- 隐藏的上级科目字段 -->
                        {{ form.parent_id(style="display: none;") }}
                    </div>
                    
                    <div class="form-group">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="3") }}
                        {% if form.description.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.description.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            可选，用于说明科目的用途和核算内容
                        </small>
                    </div>
                    
                    <div class="form-group text-center">
                        {{ form.submit(class="btn btn-primary btn-lg") }}
                        <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-secondary btn-lg">取消</a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 使用指南 -->
        <div class="financial-card">
            <div class="financial-card-header">
                <i class="fas fa-question-circle"></i> 使用指南
            </div>
            <div class="financial-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-star text-warning"></i> 创建学校科目的建议</h6>
                        <ul class="small">
                            <li><strong>在系统科目下扩展：</strong>选择系统科目作为上级，创建学校特色科目</li>
                            <li><strong>食堂收入细分：</strong>在"主营业务收入"下创建"学生餐费"、"教师餐费"等</li>
                            <li><strong>食材分类管理：</strong>在"库存商品"下创建"米面类"、"蔬菜类"、"肉类"等</li>
                            <li><strong>费用精细化：</strong>在"管理费用"下创建"水电费"、"人工费"等</li>
                        </ul>

                        <h6><i class="fas fa-code text-info"></i> 编码规则</h6>
                        <ul class="small">
                            <li>资产类：1xxx（如1001现金）</li>
                            <li>负债类：2xxx（如2201应付账款）</li>
                            <li>权益类：3xxx（如3001实收资本）</li>
                            <li>收入类：6xxx（如6001营业收入）</li>
                            <li>费用类：6xxx（如6401营业成本）</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-example text-success"></i> 食堂常用科目示例</h6>
                        <div class="small">
                            <strong>收入类扩展：</strong>
                            <ul>
                                <li>6001001 - 学生早餐收入</li>
                                <li>6001002 - 学生午餐收入</li>
                                <li>6001003 - 学生晚餐收入</li>
                                <li>6001011 - 教师餐费收入</li>
                            </ul>

                            <strong>库存商品扩展：</strong>
                            <ul>
                                <li>1401001 - 米面粮油</li>
                                <li>1401002 - 新鲜蔬菜</li>
                                <li>1401003 - 肉禽蛋类</li>
                                <li>1401004 - 调料用品</li>
                            </ul>

                            <strong>费用类扩展：</strong>
                            <ul>
                                <li>6602001 - 水电燃气费</li>
                                <li>6602002 - 厨师工资</li>
                                <li>6602003 - 设备维护费</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="alert alert-success mt-3">
                    <i class="fas fa-lightbulb"></i>
                    <strong>智能提示：</strong>
                    选择科目类型和上级科目后，系统会自动建议可用的编码，点击建议即可自动填入！
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_css %}
<style>
/* 科目树型结构样式 */
.subject-tree-container {
    background-color: #f8f9fa;
}

.subject-tree-item {
    padding: 8px 12px;
    margin: 2px 0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    user-select: none;
}

.subject-tree-item:hover {
    background-color: #e9ecef;
}

.subject-tree-item.selected {
    background-color: #007bff;
    color: white;
}

.subject-tree-item.system {
    border-left: 4px solid #28a745;
}

.subject-tree-item.school {
    border-left: 4px solid #007bff;
}

.subject-tree-item .subject-code {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

.subject-tree-item .subject-name {
    margin-left: 8px;
}

.subject-tree-item .subject-badge {
    font-size: 0.75em;
    margin-left: 8px;
}

.subject-tree-item .expand-icon {
    width: 16px;
    text-align: center;
    margin-right: 4px;
    font-size: 12px;
}

.subject-tree-item.has-children .expand-icon {
    cursor: pointer;
}

.subject-tree-children {
    margin-left: 20px;
    border-left: 1px dashed #dee2e6;
    padding-left: 10px;
}

.subject-tree-children.collapsed {
    display: none;
}

/* 科目类型过滤按钮 */
.btn-group .btn.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}
</style>
{% endblock %}

{% block financial_js %}
<script>
// 会计科目表单页面特定JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const subjectTypeSelect = document.getElementById('subject_type');
    const balanceDirectionSelect = document.getElementById('balance_direction');
    const balanceDirectionHelp = document.getElementById('balance_direction_help');
    const codeInput = document.getElementById('code');
    const nameInput = document.getElementById('name');
    const parentIdInput = document.getElementById('parent_id');

    // 创建模式相关元素
    const modeIndependent = document.getElementById('mode_independent');
    const modeExtend = document.getElementById('mode_extend');
    const extendSection = document.getElementById('extend_section');
    const baseSubjectSelect = document.getElementById('base_subject_select');

    // 科目类型与余额方向的对应关系
    const balanceDirectionMap = {
        '资产': '借方',
        '费用': '借方',
        '负债': '贷方',
        '所有者权益': '贷方',
        '收入': '贷方'
    };

    // 科目类型说明
    const subjectTypeDescriptions = {
        '资产': '资产类科目余额方向为借方，用于核算学校食堂拥有的各种资产',
        '负债': '负债类科目余额方向为贷方，用于核算学校食堂的各种债务',
        '所有者权益': '所有者权益类科目余额方向为贷方，用于核算学校食堂的净资产',
        '收入': '收入类科目余额方向为贷方，用于核算学校食堂的各种收入',
        '费用': '费用类科目余额方向为借方，用于核算学校食堂的各种支出'
    };

    // 存储所有科目数据
    let allSubjects = [];

    // 加载所有科目数据
    async function loadAllSubjects() {
        try {
            const response = await fetch('/financial/accounting-subjects/api?include_system=true');
            allSubjects = await response.json();
            updateBaseSubjectOptions();
        } catch (error) {
            console.error('加载科目数据失败:', error);
        }
    }

    // 更新基础科目选择
    function updateBaseSubjectOptions() {
        baseSubjectSelect.innerHTML = '<option value="">请选择要扩展的科目...</option>';

        // 按科目类型分组显示
        const groupedSubjects = {};
        allSubjects.forEach(subject => {
            if (!groupedSubjects[subject.subject_type]) {
                groupedSubjects[subject.subject_type] = [];
            }
            groupedSubjects[subject.subject_type].push(subject);
        });

        Object.keys(groupedSubjects).forEach(type => {
            const optgroup = document.createElement('optgroup');
            optgroup.label = `${type}类科目`;

            groupedSubjects[type].forEach(subject => {
                const option = document.createElement('option');
                option.value = subject.id;
                const badge = subject.is_system ? '[系统]' : '[学校]';
                const indent = '　'.repeat(subject.level - 1);
                option.textContent = `${badge} ${indent}${subject.code} - ${subject.name}`;
                option.style.color = subject.is_system ? '#28a745' : '#007bff';
                optgroup.appendChild(option);
            });

            baseSubjectSelect.appendChild(optgroup);
        });
    }

    // 科目类型变化时自动设置余额方向
    function updateBalanceDirection() {
        const selectedType = subjectTypeSelect.value;
        if (selectedType && balanceDirectionMap[selectedType]) {
            balanceDirectionSelect.value = balanceDirectionMap[selectedType];
            balanceDirectionHelp.textContent = subjectTypeDescriptions[selectedType];
        } else {
            balanceDirectionHelp.textContent = '余额方向将根据科目类型自动设置';
        }

        generateCodeSuggestion();
    }

    // 生成科目编码建议
    function generateCodeSuggestion() {
        const selectedType = subjectTypeSelect.value;
        const isExtendMode = modeExtend.checked;
        const baseSubjectId = baseSubjectSelect.value;

        if (!selectedType) return;

        let suggestedCode = '';

        if (isExtendMode && baseSubjectId) {
            // 扩展模式：基于选中的科目生成子科目编码
            const baseSubject = allSubjects.find(s => s.id == baseSubjectId);
            if (baseSubject) {
                const existingCodes = allSubjects
                    .filter(s => s.parent_id == baseSubjectId)
                    .map(s => s.code)
                    .sort();

                for (let i = 1; i <= 999; i++) {
                    const code = baseSubject.code + i.toString().padStart(3, '0');
                    if (!existingCodes.includes(code)) {
                        suggestedCode = code;
                        break;
                    }
                }

                // 自动设置科目类型和余额方向
                subjectTypeSelect.value = baseSubject.subject_type;
                balanceDirectionSelect.value = baseSubject.balance_direction;
                balanceDirectionHelp.textContent = `继承自 ${baseSubject.name} 的属性`;

                // 设置上级科目
                parentIdInput.value = baseSubjectId;
            }
        } else {
            // 独立模式：根据科目类型生成一级科目编码
            const typePrefix = {
                '资产': '1',
                '负债': '2',
                '所有者权益': '3',
                '收入': '6',
                '费用': '6'
            };

            const prefix = typePrefix[selectedType];
            if (prefix) {
                const existingCodes = allSubjects
                    .filter(s => s.subject_type === selectedType && s.level === 1)
                    .map(s => s.code)
                    .sort();

                for (let i = 1; i <= 999; i++) {
                    const code = prefix + i.toString().padStart(3, '0');
                    if (!existingCodes.includes(code)) {
                        suggestedCode = code;
                        break;
                    }
                }
            }

            // 清空上级科目
            parentIdInput.value = '';
        }

        if (suggestedCode) {
            codeInput.value = suggestedCode;
            codeInput.style.borderColor = '#28a745';
        }
    }

    // 创建模式切换
    modeIndependent.addEventListener('change', function() {
        if (this.checked) {
            extendSection.style.display = 'none';
            generateCodeSuggestion();
        }
    });

    modeExtend.addEventListener('change', function() {
        if (this.checked) {
            extendSection.style.display = 'block';
            generateCodeSuggestion();
        }
    });

    // 监听事件
    subjectTypeSelect.addEventListener('change', updateBalanceDirection);
    baseSubjectSelect.addEventListener('change', generateCodeSuggestion);

    // 初始化
    loadAllSubjects().then(() => {
        updateBalanceDirection();
    });

    // 表单验证
    const form = document.querySelector('.financial-form');
    form.addEventListener('submit', function(e) {
        const code = codeInput.value.trim();
        const name = nameInput.value.trim();

        if (!code) {
            alert('请输入科目编码');
            e.preventDefault();
            return false;
        }

        if (!name) {
            alert('请输入科目名称');
            e.preventDefault();
            return false;
        }

        // 检查科目编码是否重复
        const existingSubject = allSubjects.find(s => s.code === code);
        if (existingSubject) {
            alert(`科目编码 ${code} 已被使用：${existingSubject.name}`);
            e.preventDefault();
            return false;
        }

        // 显示提交状态
        const submitBtn = form.querySelector('input[type="submit"]');
        showLoading(submitBtn);
    });

    // 实时编码验证
    codeInput.addEventListener('input', function() {
        const code = this.value.trim();
        if (code) {
            const existingSubject = allSubjects.find(s => s.code === code);
            if (existingSubject) {
                this.style.borderColor = '#dc3545';
                this.title = `编码已被使用：${existingSubject.name}`;
            } else {
                this.style.borderColor = '#28a745';
                this.title = '编码可用';
            }
        } else {
            this.style.borderColor = '';
            this.title = '';
        }
    });
});
</script>
{% endblock %}
