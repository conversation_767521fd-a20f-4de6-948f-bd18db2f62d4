{% extends "financial/base.html" %}

{% block page_title %}
{% if subject %}编辑会计科目{% else %}新增会计科目{% endif %}
{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{{ url_for('financial.accounting_subjects_index') }}">会计科目管理</a></li>
<li class="breadcrumb-item active">
    {% if subject %}编辑科目{% else %}新增科目{% endif %}
</li>
{% endblock %}

{% block page_actions %}
<div class="financial-actions">
    <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> 返回列表
    </a>
</div>
{% endblock %}

{% block financial_content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="financial-card">
            <div class="financial-card-header">
                <i class="fas fa-{% if subject %}edit{% else %}plus{% endif %}"></i>
                {% if subject %}编辑会计科目{% else %}新增会计科目{% endif %}
            </div>
            <div class="financial-card-body">
                <form method="POST" class="financial-form">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.code.label(class="form-label") }}
                                {{ form.code(class="form-control" + (" is-invalid" if form.code.errors else "")) }}
                                {% if form.code.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.code.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    科目编码用于唯一标识会计科目，建议使用数字编码
                                </small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                {% if form.name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.name.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.subject_type.label(class="form-label") }}
                                {{ form.subject_type(class="form-control" + (" is-invalid" if form.subject_type.errors else ""), id="subject_type") }}
                                {% if form.subject_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.subject_type.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.balance_direction.label(class="form-label") }}
                                {{ form.balance_direction(class="form-control" + (" is-invalid" if form.balance_direction.errors else ""), id="balance_direction") }}
                                {% if form.balance_direction.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.balance_direction.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted" id="balance_direction_help">
                                    余额方向将根据科目类型自动设置
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        {{ form.parent_id.label(class="form-label") }}
                        <div class="card">
                            <div class="card-header">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    选择上级科目可以在现有科目下创建子科目，不选则创建独立的一级科目
                                </small>
                            </div>
                            <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                                <div id="subject-tree-list">
                                    <div class="text-center text-muted py-3">
                                        <i class="fas fa-spinner fa-spin"></i> 加载科目数据中...
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{ form.parent_id(style="display: none;") }}

                        <!-- 选中科目显示 -->
                        <div id="selected-parent" class="mt-2" style="display: none;">
                            <div class="alert alert-success">
                                <strong>选中上级科目：</strong>
                                <span id="selected-parent-text"></span>
                                <button type="button" class="btn btn-sm btn-outline-secondary ml-2" onclick="clearParentSelection()">
                                    <i class="fas fa-times"></i> 取消选择
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="3") }}
                        {% if form.description.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.description.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            可选，用于说明科目的用途和核算内容
                        </small>
                    </div>
                    
                    <div class="form-group text-center">
                        {{ form.submit(class="btn btn-primary btn-lg") }}
                        <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-secondary btn-lg">取消</a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 使用指南 -->
        <div class="financial-card">
            <div class="financial-card-header">
                <i class="fas fa-question-circle"></i> 使用指南
            </div>
            <div class="financial-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-star text-warning"></i> 创建学校科目的建议</h6>
                        <ul class="small">
                            <li><strong>在系统科目下扩展：</strong>选择系统科目作为上级，创建学校特色科目</li>
                            <li><strong>食堂收入细分：</strong>在"主营业务收入"下创建"学生餐费"、"教师餐费"等</li>
                            <li><strong>食材分类管理：</strong>在"库存商品"下创建"米面类"、"蔬菜类"、"肉类"等</li>
                            <li><strong>费用精细化：</strong>在"管理费用"下创建"水电费"、"人工费"等</li>
                        </ul>

                        <h6><i class="fas fa-code text-info"></i> 编码规则</h6>
                        <ul class="small">
                            <li>资产类：1xxx（如1001现金）</li>
                            <li>负债类：2xxx（如2201应付账款）</li>
                            <li>权益类：3xxx（如3001实收资本）</li>
                            <li>收入类：6xxx（如6001营业收入）</li>
                            <li>费用类：6xxx（如6401营业成本）</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-example text-success"></i> 食堂常用科目示例</h6>
                        <div class="small">
                            <strong>收入类扩展：</strong>
                            <ul>
                                <li>6001001 - 学生早餐收入</li>
                                <li>6001002 - 学生午餐收入</li>
                                <li>6001003 - 学生晚餐收入</li>
                                <li>6001011 - 教师餐费收入</li>
                            </ul>

                            <strong>库存商品扩展：</strong>
                            <ul>
                                <li>1401001 - 米面粮油</li>
                                <li>1401002 - 新鲜蔬菜</li>
                                <li>1401003 - 肉禽蛋类</li>
                                <li>1401004 - 调料用品</li>
                            </ul>

                            <strong>费用类扩展：</strong>
                            <ul>
                                <li>6602001 - 水电燃气费</li>
                                <li>6602002 - 厨师工资</li>
                                <li>6602003 - 设备维护费</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="alert alert-success mt-3">
                    <i class="fas fa-lightbulb"></i>
                    <strong>智能提示：</strong>
                    选择科目类型和上级科目后，系统会自动建议可用的编码，点击建议即可自动填入！
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_css %}
<style>
/* 科目树型结构样式 */
.subject-tree-container {
    background-color: #f8f9fa;
}

.subject-tree-item {
    padding: 8px 12px;
    margin: 2px 0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    user-select: none;
}

.subject-tree-item:hover {
    background-color: #e9ecef;
}

.subject-tree-item.selected {
    background-color: #007bff;
    color: white;
}

.subject-tree-item.system {
    border-left: 4px solid #28a745;
}

.subject-tree-item.school {
    border-left: 4px solid #007bff;
}

.subject-tree-item .subject-code {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

.subject-tree-item .subject-name {
    margin-left: 8px;
}

.subject-tree-item .subject-badge {
    font-size: 0.75em;
    margin-left: 8px;
}

.subject-tree-item .expand-icon {
    width: 16px;
    text-align: center;
    margin-right: 4px;
    font-size: 12px;
}

.subject-tree-item.has-children .expand-icon {
    cursor: pointer;
}

.subject-tree-children {
    margin-left: 20px;
    border-left: 1px dashed #dee2e6;
    padding-left: 10px;
}

.subject-tree-children.collapsed {
    display: none;
}

/* 科目类型过滤按钮 */
.btn-group .btn.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}
</style>
{% endblock %}

{% block financial_js %}
<script>
// 会计科目表单页面特定JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const subjectTypeSelect = document.getElementById('subject_type');
    const balanceDirectionSelect = document.getElementById('balance_direction');
    const balanceDirectionHelp = document.getElementById('balance_direction_help');
    const codeInput = document.getElementById('code');
    const nameInput = document.getElementById('name');
    const parentIdInput = document.getElementById('parent_id');
    const subjectTreeList = document.getElementById('subject-tree-list');
    const selectedParent = document.getElementById('selected-parent');
    const selectedParentText = document.getElementById('selected-parent-text');

    // 科目类型与余额方向的对应关系
    const balanceDirectionMap = {
        '资产': '借方',
        '费用': '借方',
        '负债': '贷方',
        '所有者权益': '贷方',
        '收入': '贷方'
    };

    // 科目类型说明
    const subjectTypeDescriptions = {
        '资产': '资产类科目余额方向为借方，用于核算学校食堂拥有的各种资产',
        '负债': '负债类科目余额方向为贷方，用于核算学校食堂的各种债务',
        '所有者权益': '所有者权益类科目余额方向为贷方，用于核算学校食堂的净资产',
        '收入': '收入类科目余额方向为贷方，用于核算学校食堂的各种收入',
        '费用': '费用类科目余额方向为借方，用于核算学校食堂的各种支出'
    };

    // 存储所有科目数据
    let allSubjects = [];
    let selectedParentId = null;

    // 加载所有科目数据
    async function loadAllSubjects() {
        try {
            const response = await fetch('/financial/accounting-subjects/api?include_system=true');
            allSubjects = await response.json();
            buildSubjectTree();
        } catch (error) {
            console.error('加载科目数据失败:', error);
            subjectTreeList.innerHTML = '<div class="text-danger">加载失败，请刷新重试</div>';
        }
    }

    // 构建科目树
    function buildSubjectTree() {
        if (allSubjects.length === 0) {
            subjectTreeList.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                    <p class="text-muted">暂无会计科目数据</p>
                    <button type="button" class="btn btn-primary" onclick="initSystemSubjects()">
                        <i class="fas fa-plus"></i> 初始化系统科目
                    </button>
                </div>
            `;
            return;
        }

        // 按科目类型分组
        const groupedSubjects = {};
        allSubjects.forEach(subject => {
            if (!groupedSubjects[subject.subject_type]) {
                groupedSubjects[subject.subject_type] = [];
            }
            groupedSubjects[subject.subject_type].push(subject);
        });

        let html = '';
        Object.keys(groupedSubjects).forEach(type => {
            html += `<div class="mb-3">`;
            html += `<h6 class="text-primary"><i class="fas fa-folder"></i> ${type}类科目</h6>`;

            // 只显示一级科目，点击后可以展开子科目
            const topLevelSubjects = groupedSubjects[type].filter(s => s.level === 1);
            topLevelSubjects.forEach(subject => {
                html += buildSubjectItem(subject, groupedSubjects[type]);
            });

            html += `</div>`;
        });

        subjectTreeList.innerHTML = html;
    }

    // 构建单个科目项
    function buildSubjectItem(subject, allTypeSubjects) {
        const badge = subject.is_system ?
            '<span class="badge badge-success badge-sm">系统</span>' :
            '<span class="badge badge-primary badge-sm">学校</span>';

        const children = allTypeSubjects.filter(s => s.parent_id === subject.id);
        const hasChildren = children.length > 0;
        const expandIcon = hasChildren ? '<i class="fas fa-chevron-right"></i>' : '<i class="fas fa-circle" style="font-size: 6px;"></i>';

        let html = `
            <div class="subject-item" data-id="${subject.id}" data-level="${subject.level}">
                <div class="d-flex align-items-center p-2 border rounded mb-1" style="cursor: pointer; margin-left: ${(subject.level - 1) * 20}px;">
                    <span class="expand-icon mr-2" style="width: 16px;">${expandIcon}</span>
                    <code class="mr-2">${subject.code}</code>
                    <span class="flex-grow-1">${subject.name}</span>
                    ${badge}
                </div>
                <div class="children" style="display: none;">
        `;

        // 递归添加子科目
        children.forEach(child => {
            html += buildSubjectItem(child, allTypeSubjects);
        });

        html += `</div></div>`;
        return html;
    }

    // 科目选择事件
    document.addEventListener('click', function(e) {
        const subjectItem = e.target.closest('.subject-item .d-flex');
        if (subjectItem) {
            const subjectDiv = subjectItem.closest('.subject-item');
            const subjectId = subjectDiv.dataset.id;
            const subject = allSubjects.find(s => s.id == subjectId);

            if (subject) {
                // 清除之前的选中状态
                document.querySelectorAll('.subject-item .d-flex').forEach(item => {
                    item.classList.remove('bg-primary', 'text-white');
                });

                // 设置新的选中状态
                subjectItem.classList.add('bg-primary', 'text-white');

                // 更新选中信息
                selectedParentId = subjectId;
                parentIdInput.value = subjectId;
                selectedParentText.textContent = `${subject.code} - ${subject.name}`;
                selectedParent.style.display = 'block';

                // 自动设置科目类型和余额方向
                subjectTypeSelect.value = subject.subject_type;
                balanceDirectionSelect.value = subject.balance_direction;
                balanceDirectionHelp.textContent = `将创建 ${subject.name} 的子科目`;

                // 生成子科目编码建议
                generateChildCode(subject);
            }
        }

        // 展开/折叠处理
        const expandIcon = e.target.closest('.expand-icon');
        if (expandIcon) {
            e.stopPropagation();
            const subjectDiv = expandIcon.closest('.subject-item');
            const childrenDiv = subjectDiv.querySelector('.children');
            const icon = expandIcon.querySelector('i');

            if (childrenDiv.style.display === 'none') {
                childrenDiv.style.display = 'block';
                icon.className = 'fas fa-chevron-down';
            } else {
                childrenDiv.style.display = 'none';
                icon.className = 'fas fa-chevron-right';
            }
        }
    });

    // 生成子科目编码
    function generateChildCode(parentSubject) {
        const existingCodes = allSubjects
            .filter(s => s.parent_id == parentSubject.id)
            .map(s => s.code)
            .sort();

        for (let i = 1; i <= 999; i++) {
            const code = parentSubject.code + i.toString().padStart(3, '0');
            if (!existingCodes.includes(code)) {
                codeInput.value = code;
                codeInput.style.borderColor = '#28a745';
                break;
            }
        }
    }

    // 清除上级科目选择
    window.clearParentSelection = function() {
        selectedParentId = null;
        parentIdInput.value = '';
        selectedParent.style.display = 'none';

        // 清除选中状态
        document.querySelectorAll('.subject-item .d-flex').forEach(item => {
            item.classList.remove('bg-primary', 'text-white');
        });

        // 重置为独立科目模式
        generateIndependentCode();
    };

    // 生成独立科目编码
    function generateIndependentCode() {
        const selectedType = subjectTypeSelect.value;
        if (!selectedType) return;

        const typePrefix = {
            '资产': '1',
            '负债': '2',
            '所有者权益': '3',
            '收入': '6',
            '费用': '6'
        };

        const prefix = typePrefix[selectedType];
        if (prefix) {
            const existingCodes = allSubjects
                .filter(s => s.subject_type === selectedType && s.level === 1)
                .map(s => s.code)
                .sort();

            for (let i = 1; i <= 999; i++) {
                const code = prefix + i.toString().padStart(3, '0');
                if (!existingCodes.includes(code)) {
                    codeInput.value = code;
                    codeInput.style.borderColor = '#28a745';
                    break;
                }
            }
        }
    }

    // 科目类型变化时自动设置余额方向
    function updateBalanceDirection() {
        const selectedType = subjectTypeSelect.value;
        if (selectedType && balanceDirectionMap[selectedType]) {
            balanceDirectionSelect.value = balanceDirectionMap[selectedType];
            balanceDirectionHelp.textContent = subjectTypeDescriptions[selectedType];
        } else {
            balanceDirectionHelp.textContent = '余额方向将根据科目类型自动设置';
        }

        // 如果没有选中上级科目，生成独立科目编码
        if (!selectedParentId) {
            generateIndependentCode();
        }
    }

    // 监听科目类型变化
    subjectTypeSelect.addEventListener('change', updateBalanceDirection);

    // 初始化系统科目
    window.initSystemSubjects = async function() {
        if (!confirm('确定要初始化系统会计科目吗？这将创建标准的会计科目体系。')) {
            return;
        }

        try {
            subjectTreeList.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                    <p class="text-muted">正在初始化系统科目...</p>
                </div>
            `;

            const response = await fetch('/financial/accounting-subjects/init-system', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
                }
            });

            const result = await response.json();

            if (result.success) {
                alert('系统科目初始化成功！');
                // 重新加载科目数据
                await loadAllSubjects();
            } else {
                alert('初始化失败：' + result.message);
                subjectTreeList.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                        <p class="text-muted">初始化失败</p>
                        <button type="button" class="btn btn-primary" onclick="initSystemSubjects()">
                            <i class="fas fa-redo"></i> 重试
                        </button>
                    </div>
                `;
            }
        } catch (error) {
            console.error('初始化系统科目失败:', error);
            alert('初始化失败，请重试');
            subjectTreeList.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-2x text-danger mb-3"></i>
                    <p class="text-muted">网络错误</p>
                    <button type="button" class="btn btn-primary" onclick="initSystemSubjects()">
                        <i class="fas fa-redo"></i> 重试
                    </button>
                </div>
            `;
        }
    };

    // 初始化
    loadAllSubjects().then(() => {
        updateBalanceDirection();
    });

    // 表单验证
    const form = document.querySelector('.financial-form');
    form.addEventListener('submit', function(e) {
        const code = codeInput.value.trim();
        const name = nameInput.value.trim();

        if (!code) {
            alert('请输入科目编码');
            e.preventDefault();
            return false;
        }

        if (!name) {
            alert('请输入科目名称');
            e.preventDefault();
            return false;
        }

        // 检查科目编码是否重复
        const existingSubject = allSubjects.find(s => s.code === code);
        if (existingSubject) {
            alert(`科目编码 ${code} 已被使用：${existingSubject.name}`);
            e.preventDefault();
            return false;
        }

        // 显示提交状态
        const submitBtn = form.querySelector('input[type="submit"]');
        if (typeof showLoading === 'function') {
            showLoading(submitBtn);
        }
    });

    // 实时编码验证
    codeInput.addEventListener('input', function() {
        const code = this.value.trim();
        if (code) {
            const existingSubject = allSubjects.find(s => s.code === code);
            if (existingSubject) {
                this.style.borderColor = '#dc3545';
                this.title = `编码已被使用：${existingSubject.name}`;
            } else {
                this.style.borderColor = '#28a745';
                this.title = '编码可用';
            }
        } else {
            this.style.borderColor = '';
            this.title = '';
        }
    });
});
</script>
{% endblock %}
